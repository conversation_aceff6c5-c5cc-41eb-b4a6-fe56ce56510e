const express = require('express');
const app = express();
const port = 3000;
const mongoose =require('mongoose');
mongoose.connect('mongodb://127.0.0.1:27017/test',{useNewUrlParser: true,});
const db = mongoose.connection;
db.on('error',(error)=>{
    console.log(error);
});
db.once('open',()=>{
    console.log('Connected to MongoDB');
});
app.listen(port, () => {
    console.log(`Example app listening on port ${port}`)
})
